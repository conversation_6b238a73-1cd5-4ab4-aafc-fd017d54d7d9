import { apiG<PERSON><PERSON><PERSON> } from "./backend-api";
import { cookies } from "next/headers";
import { UserSession } from "@askinfosec/types";

/**
 * Fetch the current user session from the API using the existing `access_token`
 * cookie. Returns `null` when unauthenticated.
 */
export async function getServerSession(): Promise<UserSession | null> {
  // Guard: if there's no access_token cookie, avoid calling the backend
  const cookieStore = await cookies();
  const token = cookieStore.get("access_token")?.value;

  if (!token) {
    return null;
  }

  try {
    const json = await apiGetJson<{ session?: UserSession }>(
      "/v1/auth/session",
    );
    return json.session ?? null;
  } catch (error) {
    // If token is invalid/expired, clear the cookie
    if (error instanceof Error && error.message.includes("Unauthorized")) {
      // Clear the invalid token cookie
      const { cookies } = await import("next/headers");
      const cookieStore = await cookies();
      cookieStore.delete("access_token");
    }
    return null;
  }
}

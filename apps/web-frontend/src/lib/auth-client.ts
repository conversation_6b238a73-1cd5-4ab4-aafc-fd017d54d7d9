import { api<PERSON><PERSON><PERSON><PERSON> } from "./backend-api";
import { api<PERSON>et<PERSON><PERSON> } from "./backend-api";
import { UserSession } from "@askinfosec/types";

export interface SendOtpInput {
  email: string;
  type?: "email-verification" | "sign-in";
}

export interface SignInOtpInput {
  email: string;
  otp: string;
  rememberMe?: boolean;
}

export function sendVerificationOtp(
  input: SendOtpInput,
): Promise<{ sent: boolean }> {
  return apiPostJson<{ sent: boolean }>(
    "/v1/auth/email-otp/send-verification-otp",
    {
      email: input.email,
      type: input.type ?? "sign-in",
    },
  );
}

export function signInWithEmailOtp(
  input: SignInOtpInput,
): Promise<{ authenticated: boolean }> {
  return apiPostJson<{ authenticated: boolean }>("/v1/auth/sign-in/email-otp", {
    email: input.email,
    otp: input.otp,
    rememberMe: input.rememberMe ?? true,
  });
}

export function logout(): Promise<{ loggedOut: boolean }> {
  return apiPostJson<{ loggedOut: boolean }>("/v1/auth/logout");
}

/**
 * Fetch the current user session from the API using the existing `access_token`
 * cookie. Returns `null` when unauthenticated.
 * This is the client-side version that doesn't use next/headers.
 */
export async function getClientSession(): Promise<UserSession | null> {
  // Guard: if there's no access_token cookie, avoid calling the backend
  const hasAccessToken = document.cookie
    .split("; ")
    .some((c) => c.startsWith("access_token="));

  if (!hasAccessToken) {
    return null;
  }

  try {
    const json = await apiGetJson<{ session?: UserSession }>(
      "/v1/auth/session",
    );
    return json.session ?? null;
  } catch (error) {
    // If token is invalid/expired, clear the cookie and logout
    if (error instanceof Error && error.message.includes("Unauthorized")) {
      // Clear the invalid token cookie by calling logout
      await logout();
    }
    return null;
  }
}

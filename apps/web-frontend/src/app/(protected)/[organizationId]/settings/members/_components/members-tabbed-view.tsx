"use client";

import React, { useState } from "react";
import { MemberDTO, MemberInviteDTO, MemberStatsDTO } from "@/types/members";
import { MembersListClient } from "./members-list-client";
import { InvitesClient } from "./invites-client";
import { RoleDistributionClient } from "./role-distribution-client";
import { Users, Clock, UserPlus, BarChart3 } from "lucide-react";
import { Button } from "@askinfosec/shadcn-ui";
import { InviteMemberDialog } from "./invite-member-dialog";

interface MembersTabbedViewProps {
  members: MemberDTO[];
  invites: MemberInviteDTO[];
  stats: MemberStatsDTO | null;
}

export function MembersTabbedView({
  members,
  invites,
  stats,
}: MembersTabbedViewProps) {
  const [activeTab, setActiveTab] = useState<"members" | "invites" | "roles">(
    "members",
  );

  const memberCount = members.length;
  const inviteCount = invites.length;

  return (
    <div className="space-y-6">
      {/* Header with Invite <PERSON> */}
      <div className="flex items-center justify-between">
        {/* <div>
          <h1 className="text-2xl font-semibold tracking-tight">Members</h1>
          <p className="text-muted-foreground">
            Manage organization members, roles, and permissions
          </p>
        </div> */}
        <InviteMemberDialog
          trigger={
            <Button>
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Member
            </Button>
          }
        />
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-border">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab("members")}
            className={`group inline-flex items-center px-1 py-4 border-b-2 font-medium text-sm ${
              activeTab === "members"
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
            }`}
          >
            <Users className="mr-2 h-4 w-4" />
            Active Members
            <span className="ml-2 inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs bg-muted text-muted-foreground">
              {memberCount}
            </span>
          </button>

          <button
            onClick={() => setActiveTab("invites")}
            className={`group inline-flex items-center px-1 py-4 border-b-2 font-medium text-sm ${
              activeTab === "invites"
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
            }`}
          >
            <Clock className="mr-2 h-4 w-4" />
            Pending Invites
            <span className="ml-2 inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs bg-muted text-muted-foreground">
              {inviteCount}
            </span>
          </button>

          <button
            onClick={() => setActiveTab("roles")}
            className={`group inline-flex items-center px-1 py-4 border-b-2 font-medium text-sm ${
              activeTab === "roles"
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
            }`}
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            Role Distribution
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === "members" ? (
          <MembersListClient members={members} />
        ) : activeTab === "invites" ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Pending Invitations</h3>
                <p className="text-sm text-muted-foreground">
                  Manage pending member invitations and resend or cancel as
                  needed
                </p>
              </div>
            </div>
            <InvitesClient invites={invites} />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Role Distribution</h3>
                <p className="text-sm text-muted-foreground">
                  View the distribution of roles across your organization
                </p>
              </div>
            </div>
            <RoleDistributionClient stats={stats} />
          </div>
        )}
      </div>
    </div>
  );
}

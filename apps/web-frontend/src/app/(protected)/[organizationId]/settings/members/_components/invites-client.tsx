"use client";

import { MemberInviteDTO } from "@/types/members";
import React, { useState } from "react";
import { But<PERSON> } from "@askinfosec/shadcn-ui";
import { RotateCcw, X, Loader2 } from "lucide-react";
import {
  resendInviteServer,
  cancelInviteServer,
} from "../_actions/manage-invite";
import { useParams } from "next/navigation";
import { toast } from "sonner";

interface InvitesClientProps {
  invites: MemberInviteDTO[];
}

export function InvitesClient({ invites }: InvitesClientProps) {
  const [loadingStates, setLoadingStates] = useState<
    Record<string, "resend" | "cancel" | null>
  >({});
  const params = useParams();
  const organizationId = params.organizationId as string;

  const handleResend = async (inviteId: string) => {
    setLoadingStates((prev) => ({ ...prev, [inviteId]: "resend" }));

    try {
      const result = await resendInviteServer(organizationId, inviteId);

      if (result.success) {
        toast.success(result.message || "Invitation resent successfully");
      } else {
        toast.error(result.error || "Failed to resend invitation");
      }
    } catch (error) {
      toast.error("Failed to resend invitation");
      console.error("Error resending invite:", error);
    } finally {
      setLoadingStates((prev) => ({ ...prev, [inviteId]: null }));
    }
  };

  const handleCancel = async (inviteId: string) => {
    setLoadingStates((prev) => ({ ...prev, [inviteId]: "cancel" }));

    try {
      const result = await cancelInviteServer(organizationId, inviteId);

      if (result.success) {
        toast.success(result.message || "Invitation cancelled successfully");
      } else {
        toast.error(result.error || "Failed to cancel invitation");
      }
    } catch (error) {
      toast.error("Failed to cancel invitation");
      console.error("Error cancelling invite:", error);
    } finally {
      setLoadingStates((prev) => ({ ...prev, [inviteId]: null }));
    }
  };

  if (invites.length === 0) {
    return (
      <div className="text-sm text-muted-foreground">No pending invites</div>
    );
  }

  return (
    <div className="rounded border overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b bg-muted/50">
            <th className="text-left p-4 font-medium">Email</th>
            <th className="text-left p-4 font-medium">Role</th>
            <th className="text-left p-4 font-medium">Invited</th>
            <th className="text-left p-4 font-medium">Expires</th>
            <th className="text-left p-4 font-medium">Actions</th>
          </tr>
        </thead>
        <tbody>
          {invites.map((invite) => {
            const isResending = loadingStates[invite.id] === "resend";
            const isCancelling = loadingStates[invite.id] === "cancel";
            const isLoading = isResending || isCancelling;

            return (
              <tr key={invite.id} className="border-b hover:bg-muted/25">
                <td className="p-4">{invite.email}</td>
                <td className="p-4 capitalize">{invite.role}</td>
                <td className="p-4">
                  {invite.invitedAt && invite.invitedAt !== ""
                    ? new Date(invite.invitedAt).toLocaleDateString()
                    : "-"}
                </td>
                <td className="p-4">
                  {invite.expiresAt && invite.expiresAt !== ""
                    ? new Date(invite.expiresAt).toLocaleDateString()
                    : "-"}
                </td>
                <td className="p-4">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleResend(invite.id)}
                      disabled={isLoading}
                      className="h-8"
                    >
                      {isResending ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <RotateCcw className="h-3 w-3" />
                      )}
                      <span className="ml-1">Resend</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCancel(invite.id)}
                      disabled={isLoading}
                      className="h-8"
                    >
                      {isCancelling ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <X className="h-3 w-3" />
                      )}
                      <span className="ml-1">Cancel</span>
                    </Button>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

import React from "react";
import { PageMetadataSetter } from "@/components/layout/page-metadata-setter";
import {
  getMembersServer,
  getInvitesServer,
  getRolesAndPermissionsServer,
} from "./_actions/get-members";
import { MembersStatsServer } from "./_components/members-stats-server";
import { MembersTabbedView } from "./_components/members-tabbed-view";
import {
  MembersStatsSkeleton,
  MembersListSkeleton,
} from "./_components/skeletons/members-sections-skeleton";

export default async function MembersPage({
  params,
}: {
  params: Promise<{ organizationId: string }>;
}) {
  const { organizationId } = await params;

  const [membersRes, invitesRes, _rolesPermsRes] = await Promise.all([
    getMembersServer(organizationId),
    getInvitesServer(organizationId),
    getRolesAndPermissionsServer(organizationId),
  ]);

  const isNotMember = membersRes.error?.includes("not a member") ?? false;

  return (
    <>
      <PageMetadataSetter
        title="Members"
        description="Manage organization members, roles, and permissions"
      />
      <div className="space-y-6">
        {isNotMember && (
          <div className="rounded border p-3 text-sm text-destructive">
            Access restricted for this organization
          </div>
        )}

        <React.Suspense fallback={<MembersStatsSkeleton />}>
          {membersRes.error ? (
            <div className="rounded border p-3 text-sm text-destructive">
              {membersRes.error}
            </div>
          ) : (
            <MembersStatsServer stats={membersRes.data?.stats ?? null} />
          )}
        </React.Suspense>

        <React.Suspense fallback={<MembersListSkeleton />}>
          {membersRes.error || invitesRes.error ? (
            <div className="space-y-4">
              {membersRes.error && (
                <div className="rounded border p-3 text-sm text-destructive">
                  Members: {membersRes.error}
                </div>
              )}
              {invitesRes.error && (
                <div className="rounded border p-3 text-sm text-destructive">
                  Invites: {invitesRes.error}
                </div>
              )}
            </div>
          ) : (
            <MembersTabbedView
              members={membersRes.data?.members ?? []}
              invites={invitesRes.data?.invites ?? []}
              stats={membersRes.data?.stats ?? null}
            />
          )}
        </React.Suspense>
      </div>
    </>
  );
}

"use server";

import { api<PERSON><PERSON><PERSON><PERSON> } from "@/lib/backend-api";
import { ContractsV1 } from "@askinfosec/types";

export interface InviteMemberRequest {
  email: string;
  role: "owner" | "admin" | "user" | "vendor";
  name?: string;
  message?: string;
  expiresIn?: number;
}

export interface InviteMemberResponse {
  success: boolean;
  data?: ContractsV1.MemberInviteDTO;
  error?: string;
}

export async function inviteMemberServer(
  organizationId: string,
  request: InviteMemberRequest,
): Promise<InviteMemberResponse> {
  try {
    // Validate required fields
    if (!request.email || !request.role) {
      return {
        success: false,
        error: "Email and role are required",
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(request.email)) {
      return {
        success: false,
        error: "Invalid email format",
      };
    }

    // Call the backend API
    const response = await api<PERSON><PERSON><PERSON><PERSON><ContractsV1.MemberInviteDTO>(
      `/v1/organizations/${organizationId}/members/invites`,
      {
        email: request.email,
        role: request.role,
        name: request.name,
        message: request.message,
        expiresIn: request.expiresIn || 7,
      },
    );

    // For now, we'll assume success if no error is thrown
    // In the future, we might want to parse the response with a schema
    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error("Failed to invite member:", error);

    // Extract error message from the error object
    let errorMessage = "Failed to send invitation";
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

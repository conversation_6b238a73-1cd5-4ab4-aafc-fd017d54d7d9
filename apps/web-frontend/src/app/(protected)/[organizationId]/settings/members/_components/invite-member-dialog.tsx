"use client";

import { useMemo, useState } from "react";
import { useParams } from "next/navigation";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@askinfosec/shadcn-ui";
import { Input } from "@askinfosec/shadcn-ui";
import { Label } from "@askinfosec/shadcn-ui";
import { Button } from "@askinfosec/shadcn-ui";
import {
  Mail,
  Shield,
  Users,
  Building,
  CheckCircle,
  Circle,
  Loader2,
} from "lucide-react";
import { inviteMemberServer } from "../_actions/invite-member";
import { toast } from "sonner";

interface InviteMemberDialogProps {
  /** Optional custom trigger. Must be a single element (no Fragment). */
  trigger?: React.ReactElement;
}

const ROLE_OPTIONS = [
  {
    id: "admin" as const,
    label: "Admin",
    description: "Can manage projects and members within the organization",
    Icon: Shield,
  },
  {
    id: "user" as const,
    label: "Member",
    description: "Can access and contribute to organization projects",
    Icon: Users,
  },
  {
    id: "vendor" as const,
    label: "Vendor",
    description: "External collaborator with limited access",
    Icon: Building,
  },
];

export function InviteMemberDialog({ trigger }: InviteMemberDialogProps) {
  const params = useParams();
  const organizationId = params.organizationId as string;

  const [open, setOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState("");
  const [selectedRole, setSelectedRole] =
    useState<(typeof ROLE_OPTIONS)[number]["id"]>("user");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEmailValid = useMemo(() => {
    if (!emailAddress) return false;
    // Simple email validation for UI enable/disable only
    return /\S+@\S+\.[\w-]+/.test(emailAddress);
  }, [emailAddress]);

  const handleSubmit = async () => {
    if (!isEmailValid || isSubmitting) return;

    setIsSubmitting(true);
    try {
      const result = await inviteMemberServer(organizationId, {
        email: emailAddress,
        role: selectedRole,
        message: `You've been invited to join our organization as a ${selectedRole}.`,
      });

      if (result.success) {
        toast.success("Invitation sent successfully!");
        setOpen(false);
        setEmailAddress("");
        setSelectedRole("user");
        // Optionally refresh the page or update the invites list
        window.location.reload();
      } else {
        toast.error(result.error || "Failed to send invitation");
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger ?? <Button>Invite Member</Button>}
      </DialogTrigger>
      <DialogContent
        // Ensure dialog never exceeds viewport and provides our own layout paddings
        className="p-0 max-w-[80vw] sm:max-w-[640px] max-h-[80vh]"
      >
        <div className="grid grid-rows-[auto_1fr_auto] max-h-[80vh]">
          <div className="sticky top-0 z-10 bg-background border-b p-6">
            <DialogHeader>
              <DialogTitle className="text-xl">
                Invite Organization Member
              </DialogTitle>
              <DialogDescription>
                Send an invitation to join this organization
              </DialogDescription>
            </DialogHeader>
          </div>

          <div className="overflow-y-auto p-6 space-y-6">
            <div className="space-y-2">
              <Label htmlFor="invite-email">Email Address</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="invite-email"
                  placeholder="Enter email address"
                  value={emailAddress}
                  onChange={(e) => setEmailAddress(e.target.value)}
                  className="pl-10"
                  type="email"
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label>Organization Role</Label>
              <div className="space-y-3">
                {ROLE_OPTIONS.map(({ id, label, description, Icon }) => (
                  <button
                    key={id}
                    type="button"
                    onClick={() => setSelectedRole(id)}
                    className={
                      "w-full text-left rounded-lg border p-4 transition focus:outline-hidden focus:ring-2 " +
                      (selectedRole === id
                        ? "border-primary ring-primary"
                        : "hover:bg-muted")
                    }
                    aria-pressed={selectedRole === id}
                  >
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        {selectedRole === id ? (
                          <CheckCircle className="h-5 w-5 text-primary" />
                        ) : (
                          <Circle className="h-5 w-5 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{label}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {description}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          <div className="border-t p-4">
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="outline" disabled={isSubmitting}>
                  Cancel
                </Button>
              </DialogClose>
              <Button
                disabled={!isEmailValid || isSubmitting}
                onClick={handleSubmit}
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isSubmitting ? "Sending..." : "Send Invitation"}
              </Button>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

export default InviteMemberDialog;

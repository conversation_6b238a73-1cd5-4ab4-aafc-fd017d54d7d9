"use client";

import React from "react";
import { MemberStatsDTO } from "@/types/members";

interface RoleDistributionClientProps {
  stats: MemberStatsDTO | null;
}

export function RoleDistributionClient({ stats }: RoleDistributionClientProps) {
  if (!stats) return null;

  const byRole = stats.byRole ?? { owner: 0, admin: 0, user: 0, vendor: 0 };
  const entries = Object.entries(byRole);

  return (
    <div className="rounded-lg border p-4">
      <div className="text-sm font-medium mb-2">Role Distribution</div>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {entries.map(([role, count]) => (
          <div key={role} className="text-center p-3 bg-muted rounded">
            <div className="text-2xl font-bold">{count}</div>
            <div className="text-xs text-muted-foreground capitalize mt-1">
              {role}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

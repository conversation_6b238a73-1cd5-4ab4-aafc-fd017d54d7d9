"use server";

import { api<PERSON><PERSON><PERSON><PERSON>, api<PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/backend-api";
import { revalidatePath } from "next/cache";

export interface ManageInviteResponse {
  success: boolean;
  error?: string;
  message?: string;
}

export async function resendInviteServer(
  organizationId: string,
  inviteId: string,
): Promise<ManageInviteResponse> {
  try {
    // Call the backend API to resend the invite
    await api<PERSON><PERSON><PERSON><PERSON>(
      `/v1/organizations/${organizationId}/members/invites/${inviteId}/resend`,
      {},
    );

    // Revalidate the members page to refresh the data
    revalidatePath(`/${organizationId}/settings/members`);

    return {
      success: true,
      message: "Invitation resent successfully",
    };
  } catch (error) {
    console.error("Failed to resend invite:", error);

    // Extract error message from the error object
    let errorMessage = "Failed to resend invitation";
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

export async function cancelInviteServer(
  organizationId: string,
  inviteId: string,
): Promise<ManageInviteResponse> {
  try {
    // Call the backend API to cancel the invite
    await apiDeleteJson(
      `/v1/organizations/${organizationId}/members/invites/${inviteId}`,
    );

    // Revalidate the members page to refresh the data
    revalidatePath(`/${organizationId}/settings/members`);

    return {
      success: true,
      message: "Invitation cancelled successfully",
    };
  } catch (error) {
    console.error("Failed to cancel invite:", error);

    // Extract error message from the error object
    let errorMessage = "Failed to cancel invitation";
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === "string") {
      errorMessage = error;
    }

    return {
      success: false,
      error: errorMessage,
    };
  }
}

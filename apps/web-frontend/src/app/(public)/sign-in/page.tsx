"use client";

import { useState, useEffect } from "react";
import { sendVerificationOtp } from "@/lib/auth-client";
import { useRouter } from "next/navigation";
import { AuthLayout } from "@/components/auth-layout";
import { <PERSON><PERSON>, Card, CardContent } from "@askinfosec/shadcn-ui";
import { useSession } from "@/providers/session-provider";

export default function SignInPage() {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<"idle" | "loading" | "sent" | "error">(
    "idle",
  );
  const [error, setError] = useState<string>("");
  const router = useRouter();
  const { session, isLoading: sessionLoading } = useSession();

  // Check if user is already logged in and redirect appropriately
  useEffect(() => {
    if (!sessionLoading && session) {
      // User is logged in, redirect to no-organization page
      // They can create/join an organization from there
      router.push("/no-organization");
    }
  }, [session, sessionLoading, router]);

  // Also check for valid client-side session on mount
  useEffect(() => {
    const checkClientSession = async () => {
      try {
        const { getClientSession } = await import("@/lib/auth-client");
        const clientSession = await getClientSession();
        if (clientSession) {
          router.push("/no-organization");
        }
      } catch {
        // Session invalid, stay on sign-in page
        console.debug("No valid client session found");
      }
    };

    // Only check if we don't already have a session from server
    if (!sessionLoading && !session) {
      checkClientSession();
    }
  }, [session, sessionLoading, router]);

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setStatus("loading");
    setError("");
    try {
      await sendVerificationOtp({ email, type: "sign-in" });
      setStatus("sent");
      // Redirect to verify page after sending OTP
      setTimeout(() => {
        router.push(`/verify?email=${encodeURIComponent(email)}`);
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send OTP");
      setStatus("error");
    }
  };

  // Show loading while checking authentication status
  if (sessionLoading) {
    return (
      <AuthLayout
        title="Welcome back"
        subtitle="Sign in to your AskInfosec account to continue"
      >
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <div className="text-muted-foreground">Loading...</div>
        </div>
      </AuthLayout>
    );
  }

  // Don't render the form if user is already logged in
  if (session) {
    return null;
  }

  return (
    <AuthLayout
      title="Welcome back"
      subtitle="Sign in to your AskInfosec account to continue"
    >
      <form onSubmit={onSubmit} className="space-y-8">
        {error && (
          <Card className="border-destructive/20 bg-destructive/10">
            <CardContent className="p-4">
              <p className="text-destructive text-sm">{error}</p>
            </CardContent>
          </Card>
        )}

        {status === "sent" && (
          <Card className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
            <CardContent className="p-4">
              <p className="text-green-700 dark:text-green-300 text-sm">
                OTP sent successfully! Redirecting to verification page...
              </p>
            </CardContent>
          </Card>
        )}

        {/* Email Input */}
        <div className="space-y-3">
          <label
            htmlFor="email"
            className="text-sm font-medium text-foreground"
          >
            Email address
          </label>
          <div className="relative">
            <svg
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            </svg>
            <input
              id="email"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              className="w-full pl-10 pr-4 py-3 border border-input rounded-lg focus:ring-2 focus:ring-ring focus:border-ring transition-colors bg-background text-foreground placeholder-muted-foreground"
            />
          </div>
        </div>

        {/* Send OTP Button */}
        <Button
          type="submit"
          disabled={status === "loading"}
          className="w-full"
        >
          {status === "loading" ? "Sending..." : "Send verification code"}
        </Button>

        {/* Sign Up Link */}
        <div className="pt-2">
          <p className="text-center text-sm text-muted-foreground">
            Don't have an account?{" "}
            <button
              type="button"
              onClick={() => router.push("/sign-up")}
              className="font-semibold text-primary hover:underline bg-transparent border-none p-0 cursor-pointer"
            >
              Sign up
            </button>
          </p>
        </div>
      </form>
    </AuthLayout>
  );
}

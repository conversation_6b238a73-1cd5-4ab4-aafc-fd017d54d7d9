import { Injectable, NotFoundException } from '@nestjs/common';
import {
  MemberRepository,
  RoleRepository,
  PermissionRepository,
  InviteRepository,
  MemberActivityRepository,
  SessionContext,
  Role,
  Permission,
  Invite,
  MemberWithRelations,
  MemberStats,
  MemberFilters,
  CreateInviteData,
} from '@askinfosec/database-drizzle';
import { getRolePermissions, type UserRole } from '@askinfosec/shared';
import { InjectResend } from '../../resend';
import { Resend } from 'resend';

export interface CreateMemberData {
  userId: string;
  roleId: string;
  departmentId?: string;
  jobTitle?: string;
  phone?: string;
  timezone?: string;
  language?: string;
  notes?: string;
  createdBy?: string;
}

export interface UpdateMemberData {
  roleId?: string;
  departmentId?: string;
  status?: string;
  jobTitle?: string;
  phone?: string;
  timezone?: string;
  language?: string;
  notes?: string;
  updatedBy?: string;
}

@Injectable()
export class MembersService {
  constructor(
    private readonly memberRepository: MemberRepository,
    private readonly roleRepository: RoleRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly inviteRepository: InviteRepository,
    private readonly memberActivityRepository: MemberActivityRepository,
    @InjectResend() private readonly resend: Resend,
  ) {}

  async getMembers(
    context: SessionContext,
    filters?: MemberFilters,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ members: MemberWithRelations[]; total: number }> {
    const { members, total } = await this.memberRepository.findByOrganization(
      context,
      filters,
      page,
      limit,
    );

    return { members, total };
  }

  async getMemberStats(context: SessionContext): Promise<MemberStats> {
    const stats = await this.memberRepository.getStats(context);
    return stats;
  }

  async getCurrentMember(context: SessionContext): Promise<any> {
    if (!context) {
      throw new Error('Session context is required');
    }

    const member =
      await this.memberRepository.findByUserAndOrganizationWithRelations(
        context,
      );

    if (!member) {
      throw new NotFoundException('Member not found');
    }

    // Get permissions for the member's role from database
    const databasePermissions = await this.roleRepository.getPermissionsForRole(
      member.role.id,
      context,
    );

    // Use hard-coded permissions as fallback if database is empty
    const userRole = member.role.name.toLowerCase() as UserRole;
    const allPermissions = getRolePermissions(
      userRole,
      databasePermissions.map((p) => p.id),
    );

    // Transform the backend MemberWithRelations to match frontend Member type
    return {
      id: member.id,
      userId: member.userId,
      organizationId: member.organizationId,
      email: member.user.email || '',
      name: member.user.name || '',
      image: member.user.image || undefined,
      role: userRole,
      permissions: allPermissions, // Return merged permissions (database + hard-coded)
      status: member.status,
      joinedAt: member.joinedAt.toISOString(),
      lastActiveAt: member.lastActiveAt?.toISOString(),
      lastLoginAt: member.lastLoginAt?.toISOString(),
      twoFactorEnabled: member.twoFactorEnabled,
      emailVerified: member.emailVerified,
      department: member.department?.name,
      jobTitle: member.jobTitle,
      phone: member.phone,
      timezone: member.timezone,
      language: member.language,
      notes: member.notes,
      createdBy: member.createdById,
      updatedAt: member.updatedAt.toISOString(),
    };
  }

  async getMemberById(
    id: string,
    context: SessionContext,
  ): Promise<MemberWithRelations | null> {
    const member = await this.memberRepository.findById(id, context);
    return member as MemberWithRelations | null;
  }

  async createMember(
    data: CreateMemberData,
    context: SessionContext,
  ): Promise<MemberWithRelations> {
    // Check if member already exists
    const existingMember =
      await this.memberRepository.findByUserAndOrganization(context);

    if (existingMember) {
      return existingMember as MemberWithRelations;
    }

    // Verify role exists
    const role = await this.roleRepository.findById(data.roleId, context);
    if (!role) {
      throw new Error('Role not found');
    }

    const member = await this.memberRepository.create(data, context);
    return member;
  }

  async updateMember(
    id: string,
    data: UpdateMemberData,
    context: SessionContext,
  ): Promise<MemberWithRelations> {
    const existingMember = await this.memberRepository.findById(id, context);
    if (!existingMember) {
      throw new NotFoundException('Member not found');
    }

    // Verify role exists if updating role
    if (data.roleId) {
      const role = await this.roleRepository.findById(data.roleId, context);
      if (!role) {
        throw new Error('Role not found');
      }
    }

    const member = await this.memberRepository.update(id, data, context);
    return member;
  }

  async deleteMember(id: string, context: SessionContext): Promise<void> {
    const existingMember = await this.memberRepository.findById(id, context);
    if (!existingMember) {
      throw new NotFoundException('Member not found');
    }

    await this.memberRepository.delete(id, context);
  }

  async getRoles(context: SessionContext): Promise<any[]> {
    const rolesWithCount =
      await this.roleRepository.findByOrganization(context);

    // Get all permissions for the organization
    const allPermissions = await this.permissionRepository.findAll(context);

    // Convert RoleWithMemberCount to Role for frontend compatibility
    return rolesWithCount.map((role) => {
      // Map role name to UserRole type
      const userRole = role.name.toLowerCase() as
        | 'owner'
        | 'admin'
        | 'user'
        | 'vendor';

      // For now, return a simplified role structure that matches frontend expectations
      // In a real implementation, you would fetch the actual permissions for each role
      return {
        id: userRole, // Use the role name as ID for frontend compatibility
        name: role.name,
        description: role.description || '',
        permissions: [], // TODO: Fetch actual permissions for this role
        isDefault: role.isSystem,
        isSystem: role.isSystem,
        memberCount: role.memberCount,
      };
    });
  }

  async getPermissions(context: SessionContext): Promise<Permission[]> {
    const permissions = await this.permissionRepository.findAll(context);
    return permissions as any;
  }

  async getInvites(
    context: SessionContext,
    filters?: any,
    page: number = 1,
    limit: number = 20,
  ): Promise<{ invites: any[]; total: number }> {
    const result = await this.inviteRepository.findByOrganization(
      context,
      filters,
      page,
      limit,
    );

    // Map database fields to contract fields
    const mappedInvites = result.invites.map((invite) => ({
      id: invite.id,
      email: invite.email,
      role: invite.roleId, // Map roleId to role
      invitedAt: invite.createdAt?.toISOString() || '', // Map createdAt to invitedAt
      expiresAt: invite.expiredAt?.toISOString() || '', // Map expiredAt to expiresAt
      status: 'pending', // Default status
      token: invite.token,
      invitedBy: invite.createdBy,
    }));

    return {
      invites: mappedInvites,
      total: result.total,
    };
  }

  async createInvite(
    data: CreateInviteData,
    context: SessionContext,
  ): Promise<Invite> {
    // Create the invite in the database
    const invite = await this.inviteRepository.create(data, context);

    // Send invitation email
    await this.sendInvitationEmail(invite, context);

    return invite as any;
  }

  private async sendInvitationEmail(
    invite: Invite,
    context: SessionContext,
  ): Promise<void> {
    try {
      const frontendBase = process.env.FRONTEND_URL || 'http://localhost:3005';
      const inviteUrl = `${frontendBase}/accept-invite?token=${invite.token}&org=${context.organizationId}`;

      // Get the role name for the email
      const role = await this.roleRepository.findById(invite.roleId, context);
      const roleName = role?.name || 'member';

      const subject = `You're invited to join our organization on AskInfosec`;
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">You're invited to join our organization</h2>
          <p>Hello,</p>
          <p>You've been invited to join our organization as a <strong>${roleName}</strong> on AskInfosec.</p>
          <p style="margin: 30px 0;">
            <a href="${inviteUrl}" style="background-color: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Accept Invitation
            </a>
          </p>
          <p>Or copy and paste this URL into your browser:</p>
          <p style="word-break: break-all; color: #666;">${inviteUrl}</p>
          <p style="margin-top: 30px; color: #666; font-size: 14px;">
            This invitation will expire in 7 days. If you weren't expecting this invitation, you can safely ignore this email.
          </p>
        </div>
      `;

      const text = `You've been invited to join our organization as a ${roleName} on AskInfosec. Click the link to accept: ${inviteUrl}`;

      await this.resend.emails.send({
        from: `AskInfosec <${process.env.RESEND_EMAIL_FROM}>`,
        to: invite.email || '',
        subject,
        html,
        text,
      });
    } catch (error) {
      console.error('Failed to send invitation email:', error);
      // Don't throw error to avoid breaking the invite creation
      // The invite is still created, just the email failed
    }
  }

  async deleteInvite(id: string, context: SessionContext): Promise<void> {
    await this.inviteRepository.delete(id, context);
  }

  async resendInvite(id: string, context: SessionContext): Promise<Invite> {
    const invite = await this.inviteRepository.resend(id, context);

    // Send the invitation email again
    await this.sendInvitationEmail(invite, context);

    return invite as any;
  }

  async bulkAction(
    action: string,
    memberIds: string[],
    context: SessionContext,
  ): Promise<void> {
    switch (action) {
      case 'activate':
        for (const id of memberIds) {
          await this.memberRepository.update(id, { status: 'active' }, context);
        }
        break;
      case 'deactivate':
        for (const id of memberIds) {
          await this.memberRepository.update(
            id,
            { status: 'inactive' },
            context,
          );
        }
        break;
      case 'delete':
        for (const id of memberIds) {
          await this.memberRepository.delete(id, context);
        }
        break;
      default:
        throw new Error(`Unknown bulk action: ${action}`);
    }
  }

  async logActivity(
    memberId: string,
    type: string,
    description: string,
    context: SessionContext,
    metadata?: Record<string, any>,
  ): Promise<void> {
    await this.memberActivityRepository.create(
      {
        memberId,
        type,
        description,
        metadata,
        timestamp: new Date(),
      },
      context,
    );
  }
}

import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  UseGuards,
  Version,
  Res,
} from '@nestjs/common';
import { Session } from '../decorators/session.decorator';
import type { UserSession } from '@askinfosec/types';
import { ApiSurface } from '../../../common/decorators/api-surface.decorator';
import { InjectResend } from '../../resend';
import { Resend } from 'resend';
import jwt from 'jsonwebtoken';
import { Response } from 'express';
import { appConfig } from '../../../config';
import { CustomAuthGuard } from '../guards/custom-auth.guard';
import { BetterAuthGuard } from '../guards/better-auth.guard';
import { AuthDbContextGuard } from '../guards/auth-db-context.guard';
import { UsersService } from '../../users/services/users.service';
import { AuthServices } from '../services/auth.services';

@Controller({ path: 'auth' })
export class AuthController {
  constructor(
    @InjectResend() private readonly resend: Resend,
    private readonly usersService: UsersService,
    private readonly authServices: AuthServices,
  ) {}

  @ApiSurface('external')
  @Version('1')
  @UseGuards(BetterAuthGuard)
  @Get('me')
  async me(@Session() session: UserSession) {
    return { user: session.user };
  }

  /**
   * GET /v1/auth/session  (external surface)
   * -------------------------------------------------------------
   * Returns the session decoded from our **custom JWT** stored in the
   * `access_token` HttpOnly cookie. Uses {@link CustomAuthGuard} for
   * verification. On success the decoded payload is returned, e.g.
   * `{ user: { id, email }, session: { id, expiresAt } }`.
   *
   * Errors:
   * – 401 Unauthorized when the cookie is missing / invalid / expired.
   */
  @ApiSurface('external')
  @Version('1')
  @UseGuards(CustomAuthGuard)
  @Get('session')
  async getSession(@Session() session: UserSession) {
    return { session };
  }

  @ApiSurface('external')
  @Version('1')
  @UseGuards(AuthDbContextGuard)
  @Post('email-otp/send-verification-otp')
  async sendVerificationOtp(
    @Body()
    body: {
      email?: string;
      type?: 'email-verification' | 'sign-in' | 'forget-password';
    },
  ) {
    const rawEmail = (body as any)?.email;
    const email = typeof rawEmail === 'string' ? rawEmail.trim() : '';
    const type = body?.type;

    if (!email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      throw new BadRequestException('Invalid email');
    }

    const allowedTypes = [
      'email-verification',
      'sign-in',
      'forget-password',
    ] as const;
    if (!type || !allowedTypes.includes(type)) {
      throw new BadRequestException('Invalid type');
    }

    const otp = Array.from({ length: 6 }, () =>
      Math.floor(Math.random() * 10),
    ).join('');

    const subjectMap: Record<(typeof allowedTypes)[number], string> = {
      'sign-in': 'Your sign-in code',
      'email-verification': 'Verify your email',
      'forget-password': 'Password reset code',
    };

    const subject = `AskInfosec: ${subjectMap[type]}`;
    const frontendBase = process.env.FRONTEND_URL || 'http://localhost:3005';
    const verifyUrl = `${frontendBase}/verify?email=${encodeURIComponent(
      email,
    )}&otp=${encodeURIComponent(otp)}`;
    const html = `<p>Your OTP is <strong>${otp}</strong>. It expires in 5 minutes.</p><p>Or click the link to verify: <a href="${verifyUrl}">${verifyUrl}</a></p>`;
    const text = `Your OTP is ${otp}. It expires in 5 minutes.`;

    await this.authServices.storeEmailOtp(email, otp, type, {
      organizationId: undefined,
      userId: undefined,
      bypassRls: true,
    });

    await this.resend.emails.send({
      from: `AskInfosec <${process.env.RESEND_EMAIL_FROM}>`,
      to: email,
      subject,
      html,
      text,
    });

    // Note: Storage/verification of OTP is handled by your auth flow. This endpoint only sends the OTP.
    return { sent: true };
  }

  @ApiSurface('external')
  @Version('1')
  @UseGuards(AuthDbContextGuard)
  @Post('sign-in/email-otp')
  async signInWithEmailOtp(
    @Body() body: { email?: string; otp?: string; rememberMe?: boolean },
    @Body('rememberMe') rememberMe?: boolean,
    @Res({ passthrough: true }) res?: Response,
  ) {
    const email = typeof body?.email === 'string' ? body.email.trim() : '';
    const otp = typeof body?.otp === 'string' ? body.otp.trim() : '';

    if (!email || !/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) {
      throw new BadRequestException('Invalid email');
    }
    if (!otp) {
      throw new BadRequestException('Invalid otp');
    }

    // Verify OTP against DB (exists, not used, not expired) and consume it
    const verifiedUser = await this.authServices.verifyAndConsumeEmailOtp(
      email,
      otp,
      {
        organizationId: undefined,
        userId: undefined,
        bypassRls: true,
      },
    );

    const nowSec = Math.floor(Date.now() / 1000);
    const expiresInSec = rememberMe ? 60 * 60 * 24 * 30 : 60 * 60 * 24; // 30d or 24h

    // Get the actual user from database to get the real user ID
    const user = verifiedUser ?? (await this.usersService.findByEmail(email));
    if (!user) {
      // Auto-create user if they don't exist (email-only signup)
      const newUser = await this.usersService.create({ email });
      console.log(`[AuthController] Auto-created user for email: ${email}`);

      const payload = {
        user: { id: newUser.id, email: newUser.email },
        // TODO: FEATURE - Add organization context to session if user has default organization
        // organization: { id: user.defaultOrganizationId },
        session: {
          id: `${nowSec}-${Math.random().toString(36).slice(2, 10)}`,
          expiresAt: nowSec + expiresInSec,
        },
      };

      const accessToken = jwt.sign(payload as any, appConfig.jwt.secret, {
        expiresIn: expiresInSec,
        audience: 'askinfosec-web',
        issuer: 'askinfosec-api',
        subject: email,
      });

      // Set HttpOnly cookie so frontend has the access token
      const secure = process.env.NODE_ENV === 'production';
      const sameSite: 'lax' | 'strict' | 'none' = secure ? 'none' : 'lax';
      const domain =
        process.env.NODE_ENV === 'production' ? undefined : 'localhost';
      // TODO: Add a refresh token to the response.
      // TODO: Consider encrypting the user object in the access token.
      // TODO: Once there is already a <SessionProvider> in the frontend, we can use that to store user object.
      res?.cookie('access_token', accessToken, {
        httpOnly: true,
        secure,
        sameSite,
        maxAge: expiresInSec * 1000,
        path: '/',
        domain,
      });

      return { authenticated: true };
    }

    const payload = {
      user: { id: user.id, email: user.email },
      // TODO: FEATURE - Add organization context to session if user has default organization
      // organization: { id: user.defaultOrganizationId },
      session: {
        id: `${nowSec}-${Math.random().toString(36).slice(2, 10)}`,
        expiresAt: nowSec + expiresInSec,
      },
    };

    const accessToken = jwt.sign(payload as any, appConfig.jwt.secret, {
      expiresIn: expiresInSec,
      audience: 'askinfosec-web',
      issuer: 'askinfosec-api',
      subject: email,
    });

    // Set HttpOnly cookie so frontend has the access token
    const secure = process.env.NODE_ENV === 'production';
    const sameSite: 'lax' | 'strict' | 'none' = secure ? 'none' : 'lax';
    const domain =
      process.env.NODE_ENV === 'production' ? undefined : 'localhost';
    // TODO: Add a refresh token to the response.
    // TODO: Consider encrypting the user object in the access token.
    // TODO: Once there is already a <SessionProvider> in the frontend, we can use that to store user object.
    res?.cookie('access_token', accessToken, {
      httpOnly: true,
      secure,
      sameSite,
      maxAge: expiresInSec * 1000,
      path: '/',
      domain,
    });

    return { authenticated: true };
  }

  @ApiSurface('external')
  @Version('1')
  @Post('logout')
  async logout(@Res({ passthrough: true }) res?: Response) {
    const secure = process.env.NODE_ENV === 'production';
    const sameSite: 'lax' | 'strict' | 'none' = secure ? 'none' : 'lax';

    // Clear cookie by setting Max-Age to 0 and matching attributes
    res?.cookie('access_token', '', {
      httpOnly: true,
      secure,
      sameSite,
      maxAge: 0,
      path: '/',
    });

    return { loggedOut: true };
  }
}

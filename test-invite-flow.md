# Member Invitation Feature - Testing Guide

## Overview
This document provides a comprehensive testing guide for the newly implemented member invitation feature.

## Feature Components

### 1. Frontend Integration ✅
- **Location**: `apps/web-frontend/src/app/(protected)/[organizationId]/settings/members/_components/members-list-client.tsx`
- **Changes**: Added "Invite Member" button with UserPlus icon in the members list header
- **Dialog**: Integrated existing `InviteMemberDialog` component

### 2. Frontend-Backend Connection ✅
- **Server Action**: `apps/web-frontend/src/app/(protected)/[organizationId]/settings/members/_actions/invite-member.ts`
- **Features**:
  - Email validation
  - Role selection (owner, admin, user, vendor)
  - Error handling with user-friendly messages
  - Success feedback with toast notifications

### 3. Backend API Implementation ✅
- **Endpoint**: `POST /v1/organizations/:organizationId/members/invites`
- **Controller**: `apps/api/src/modules/members/controllers/members.controller.ts`
- **Service**: `apps/api/src/modules/members/services/members.service.ts`
- **Features**:
  - Validates invitation data
  - Creates invite record in database
  - Sends invitation email via Resend

### 4. Email Integration ✅
- **Service**: Enhanced `MembersService.createInvite()` method
- **Email Provider**: Resend integration
- **Template**: HTML email with invitation link and role information
- **Features**:
  - Professional email template
  - Secure invitation token
  - 7-day expiration
  - Fallback text version

### 5. Database Integration ✅
- **Repository**: Uses existing `InviteRepository` from `@askinfosec/database-drizzle`
- **Schema**: Leverages existing `invites` table
- **Features**:
  - Automatic token generation
  - Expiration date handling
  - Organization and role associations

## Testing Instructions

### Prerequisites
1. Ensure the application is running:
   - Frontend: `http://localhost:3005`
   - API: Backend API server running
2. Have valid organization access
3. Configure environment variables:
   - `RESEND_API_KEY`: Your Resend API key
   - `RESEND_EMAIL_FROM`: Sender email address
   - `FRONTEND_URL`: Frontend base URL

### Manual Testing Steps

#### 1. Access Members Page
1. Navigate to `/[organizationId]/settings/members`
2. Verify the "Invite Member" button appears in the top-right corner
3. Button should have UserPlus icon and "Invite Member" text

#### 2. Open Invitation Dialog
1. Click the "Invite Member" button
2. Dialog should open with:
   - Email input field with mail icon
   - Role selection (Admin, Member, Vendor)
   - Cancel and "Send Invitation" buttons

#### 3. Test Form Validation
1. Try submitting without email - button should be disabled
2. Enter invalid email format - button should remain disabled
3. Enter valid email - button should become enabled

#### 4. Send Invitation
1. Enter valid email address
2. Select appropriate role
3. Click "Send Invitation"
4. Should see loading state with spinner
5. Should see success toast notification
6. Dialog should close and form should reset
7. Page should refresh to show updated invites list

#### 5. Verify Email Delivery
1. Check the recipient's email inbox
2. Email should contain:
   - Professional AskInfosec branding
   - Clear invitation message with role
   - "Accept Invitation" button
   - Fallback URL for manual copy-paste
   - 7-day expiration notice

#### 6. Check Database
1. Verify invite record created in `invites` table
2. Should contain:
   - Unique token
   - Correct email and role
   - Organization association
   - Future expiration date

### Error Testing

#### 1. Network Errors
- Disconnect internet and try sending invite
- Should show error toast with appropriate message

#### 2. Invalid Data
- Try sending invite with malformed email
- Should show validation error

#### 3. Duplicate Invites
- Send invite to same email twice
- Should handle gracefully (check backend logic)

### API Testing

#### Using curl:
```bash
curl -X POST "http://localhost:3001/v1/organizations/YOUR_ORG_ID/members/invites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "email": "<EMAIL>",
    "role": "user",
    "message": "Welcome to our team!"
  }'
```

## Success Criteria

### ✅ Frontend
- [x] Invite button visible and accessible
- [x] Dialog opens and closes properly
- [x] Form validation works correctly
- [x] Loading states display properly
- [x] Success/error feedback shown
- [x] Toast notifications working

### ✅ Backend
- [x] API endpoint accepts requests
- [x] Data validation implemented
- [x] Database records created
- [x] Email sending functional
- [x] Error handling implemented

### ✅ Integration
- [x] End-to-end flow works
- [x] No TypeScript errors
- [x] No build errors
- [x] Proper error propagation

## Known Limitations

1. **Email Template**: Currently uses basic HTML template - could be enhanced with React Email components
2. **Invite Acceptance**: Acceptance flow not implemented in this phase
3. **Bulk Invites**: Single invite only - bulk functionality could be added
4. **Custom Messages**: Basic message support - could be enhanced with rich text

## Next Steps

1. Implement invite acceptance flow
2. Add bulk invitation functionality
3. Enhance email template with React Email
4. Add invite management (cancel, resend)
5. Add audit logging for invitation activities

## Troubleshooting

### Common Issues
1. **Toast not showing**: Ensure Toaster component is in layout
2. **Email not sending**: Check Resend API key and sender email
3. **Button disabled**: Verify email validation regex
4. **API errors**: Check network tab for detailed error messages

### Debug Steps
1. Check browser console for JavaScript errors
2. Check network tab for API request/response
3. Check server logs for backend errors
4. Verify environment variables are set correctly
